import {
  CreateV<PERSON>orD<PERSON>,
  VendorAddressDto,
  VendorDto,
  VendorTagDto,
} from "@optiwise/dto";
import { DbFactory } from "@optiwise/express";
import { withTransaction } from "@optiwise/utils";
import { Pool, PoolClient } from "pg";

export default class VendorRepository {
  constructor(private dbFactory: DbFactory) {}

  static async insertVendor(
    client: PoolClient,
    vendor: CreateVendorDto
  ): Promise<VendorDto> {
    const result = await client.query(
      `INSERT INTO vendors (name, email, phone, type, company_name, gst_number, company_phones, company_emails, payment_term_id, created_at, updated_at)
     VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,NOW(),NOW())
     RETURNING id, name, email, phone, type, company_name AS "companyName", gst_number AS "gstNumber", company_phones AS "companyPhones", company_emails AS "companyEmails", payment_term_id AS "paymentTermsId", created_at, updated_at`,
      [
        vendor.name,
        vendor.email,
        vendor.phone,
        vendor.type,
        vendor.companyName,
        vendor.gstNumber,
        vendor.companyPhones,
        vendor.companyEmails,
        vendor.paymentTermId,
      ]
    );

    return result.rows[0];
  }

  static async insertAddresses(
    client: PoolClient,
    vendorId: string,
    addresses: VendorAddressDto[]
  ) {
    await Promise.all(
      addresses.map((addr) =>
        client.query(
          `INSERT INTO vendor_addresses (vendor_id, line1, line2, city, state, country, pin)
         VALUES ($1,$2,$3,$4,$5,$6,$7)`,
          [
            vendorId,
            addr.line1,
            addr.line2 || null,
            addr.city,
            addr.state,
            addr.country,
            addr.pin,
          ]
        )
      )
    );
  }

  static async insertTags(
    client: PoolClient,
    vendorId: string,
    tags: VendorTagDto[]
  ) {
    await Promise.all(
      tags.map((tag) =>
        client.query(
          `INSERT INTO vendor_tags (vendor_id, tag_id)
         VALUES ($1,$2)`,
          [vendorId, tag.tagId]
        )
      )
    );
  }

  async create(vendor: CreateVendorDto, dbPoolKey: string): Promise<VendorDto> {
    const pool: Pool = this.dbFactory[dbPoolKey];

    return withTransaction(pool, async (client) => {
      // Insert vendor
      const createdVendor = await VendorRepository.insertVendor(client, vendor);

      // Insert addresses
      if (vendor.addresses && vendor.addresses.length > 0) {
        await VendorRepository.insertAddresses(
          client,
          createdVendor.id,
          vendor.addresses
        );
        createdVendor.addresses = vendor.addresses;
      }

      // Insert tags
      if (vendor.tags && vendor.tags.length > 0) {
        await VendorRepository.insertTags(
          client,
          createdVendor.id,
          vendor.tags
        );
        createdVendor.tags = vendor.tags;
      }

      return createdVendor;
    });
  }
}
