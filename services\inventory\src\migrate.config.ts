import type { RunnerOption } from "node-pg-migrate";

const config: RunnerOption = {
  dir: "inventory/src/migration",
  migrationsTable: "pgmigrations",
  direction: "up",
  databaseUrl: process.env.DATABASE_URL as string,
};

export default config;

/*
File generated by running:
npx node-pg-migrate create create_products \
  -r ts-node/register \
  --config src/migrate.config.ts \
  --migrations-dir src/migration \
  --file-extension ts

// To run migrations:
  npx node-pg-migrate up \
  -r ts-node/register \
  --config src/migrate.config.ts \
  --migrations-dir src/migrations \
  --file-extension ts


*/
