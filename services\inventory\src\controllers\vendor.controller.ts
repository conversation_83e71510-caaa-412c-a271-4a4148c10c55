import { CreateVendorDto } from "@optiwise/dto";
import { Request, Response } from "express";
import async<PERSON>and<PERSON> from "express-async-handler";

import { VendorService } from "../services";

export default class VendorController {
  constructor(private vendorService: VendorService) {}

  createVendor = asyncHandler(
    async (req: Request<unknown, unknown, CreateVendorDto>, res: Response) => {
      const vendor = await this.vendorService.createVendor(
        req.body,
        req.dbPoolKey
      );
      res.status(201).json(vendor);
    }
  );
}
