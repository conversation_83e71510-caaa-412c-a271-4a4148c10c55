import { Router, populateDbPool } from "@optiwise/express";
import swaggerUi from "swagger-ui-express";

import { config } from "../config";
import dbFactory from "../config/db";
import { openApiDocument } from "../openapi";
import productRouter from "./ProductRoutes";
import vendorRouter from "./vendor.route";

const router: Router = Router();

const openApiWithSecurity = {
  ...openApiDocument,
  components: {
    ...openApiDocument.components,
    securitySchemes: {
      bearerAuth: {
        type: "http",
        scheme: "bearer",
        bearerFormat: "JWT",
      },
    },
  },
};
router.use("/docs", swaggerUi.serve, swaggerUi.setup(openApiWithSecurity));

const routes: { path: string; route: Router }[] = [
  {
    path: "/products",
    route: productRouter,
  },
  {
    path: "/vendors",
    route: vendorRouter,
  },
];

router.use(populateDbPool(dbFactory, config.postgres.database));

routes?.forEach((route) => {
  router.use(route.path, route.route);
});

export default router;
