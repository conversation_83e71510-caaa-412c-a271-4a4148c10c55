// // libs/dto/src/products/schemas/updateProduct.schema.ts
// // import { z } from "../../utils/zod";
// // import { ProductSchema } from "./product.schema";
// // export const UpdateProductSchema = ProductSchema.partial()
// //   .extend({
// //     id: z.number().int().positive(),
// //   })
// //   .openapi({ title: "UpdateProduct" });
// // export type UpdateProductType = z.infer<typeof UpdateProductSchema>;
// import { z } from "../../utils/zod";
// import { ProductSchema } from "./product.schema";
// // Reuse sub-schemas from createProduct.schema.ts if possible
// const FormSchema = z.object({
//   form_id: z.string().uuid(),
//   ref_form_data: z.record(z.string(), z.any()),
// });
// const ThresholdSchema = z.object({
//   value: z.number().int(),
//   color: z.string(),
//   tag: z.string(),
// });
// const StoreSchema = z.object({
//   store_id: z.string().uuid(),
//   type: z.enum(["scrap", "finished", "semi_finished", "reject"]),
//   quantity: z.number().int().optional(),
//   scrap: z.number().int().optional(),
//   semi_finished_quantity: z.number().int().optional(),
//   rejected_quantity: z.number().int().optional(),
// });
// // Recursive children schema for update (partial)
// const UpdateProductSchema: z.ZodType<unknown> = z.lazy(() =>
//   ProductSchema.partial()
//     .extend({
//       id: z.number().int().positive(),
//       forms: z.array(FormSchema).optional(),
//       thresholds: z.array(ThresholdSchema).optional(),
//       attachments: z.array(z.string().uuid()).optional(),
//       stores: z.array(StoreSchema).optional(),
//       vendors: z.array(z.string().uuid()).optional(),
//       children: z.lazy(() => z.array(UpdateProductSchema)).optional(),
//     })
//     .openapi({ title: "UpdateProduct" })
// );
// export { UpdateProductSchema };
// export type UpdateProductType = z.infer<typeof UpdateProductSchema>;
import { z } from "../../utils/zod";
import { ProductSchema } from "./product.schema";

// Sub-schemas
const FormSchema = z.object({
  form_id: z.string().uuid(),
  ref_form_data: z.record(z.string(), z.any()),
});

const ThresholdSchema = z.object({
  value: z.number().int(),
  color: z.string(),
  tag: z.string(),
});

const StoreSchema = z.object({
  store_id: z.string().uuid(),
  type: z.enum(["scrap", "finished", "semi_finished", "reject"]),
  quantity: z.number().int().optional(),
  scrap: z.number().int().optional(),
  semi_finished_quantity: z.number().int().optional(),
  rejected_quantity: z.number().int().optional(),
});

// Recursive children schema for update (partial)
export const baseUpdateProductSchema = ProductSchema.partial().extend({
  id: z.number().int().positive(),
  forms: z.array(FormSchema).optional(),
  thresholds: z.array(ThresholdSchema).optional(),
  attachments: z.array(z.string().uuid()).optional(),
  stores: z.array(StoreSchema).optional(),
  vendors: z.array(z.string().uuid()).optional(),
  // children will be added recursively below
});

type BaseUpdateProductType = z.infer<typeof baseUpdateProductSchema>;

export const UpdateProductSchema: z.ZodType<BaseUpdateProductType> =
  baseUpdateProductSchema
    .extend({
      children: z.lazy(() => z.array(UpdateProductSchema)).optional(),
    })
    .openapi({ title: "UpdateProduct" });

export type UpdateProductType = z.infer<typeof UpdateProductSchema>;
