import { CreateProductType } from "@optiwise/dto";
import { DbFactory } from "@optiwise/express";
import { withTransaction } from "@optiwise/utils";
import { PoolClient } from "pg";

export default class ProductRepository {
  constructor(private dbFactory: DbFactory) {}

  /**
   * Create multiple products from hierarchical payload
   */
  async createProducts(payload: CreateProductType) {
    const pool = await this.dbFactory.inventory;

    return withTransaction(pool, async (client: PoolClient) => {
      // Map row_id -> DB-generated product id (after insertion)
      const rowIdToId = new Map<string, string>();
      // eslint-disable-next-line no-restricted-syntax
      for (const p of payload) {
        // Determine parent_id / top_parent_id
        let parentId: string | null = null;
        let topParentId: string | null = null;

        if (p.level > 1) {
          const parentRowId = p.row_id.split(".").slice(0, -1).join(".");
          parentId = rowIdToId.get(parentRowId)!;
          topParentId = rowIdToId.get(p.row_id.split(".")[0])!;
        }

        // Insert product and get generated id
        const productInsert = await client.query(
          `
          INSERT INTO products (
            name, item_id, product_category, category, item_type,
            uom, default_buy_price, default_sell_price, tax_percentage,
            hsn_code, description, thumbnail_id, is_used, requires_inspections,
            quantity_threshold, quantity, semifinished_quantity,
            scrap, rejected_quantity, additional_fields,
            parent_id, top_parent_id, level , row_id, status
          )
          VALUES (
            $1,$2,$3,$4,$5,
            $6,$7,$8,$9,$10,
            $11,$12,$13,$14,$15,
            $16,$17,$18,$19,$20,
            $21,$22,$23,$24,$25
          )
          RETURNING id
          `,
          [
            p.name,
            p.item_id ?? null,
            p.product_category ?? null,
            p.category ?? null,
            p.item_type ?? null,
            p.uom ?? null,
            p.default_buy_price ?? null,
            p.default_sell_price ?? null,
            p.tax_percentage ?? null,
            p.hsn_code ?? null,
            p.description ?? null,
            p.thumbnail ?? null,
            p.is_used ?? false,
            p.requires_inspections ?? false,
            p.quantity_threshold ?? 0,
            p.quantity ?? 0,
            p.semifinished_quantity ?? 0,
            p.scrap ?? 0,
            p.rejected_quantity ?? 0,
            p.additional_fields ? JSON.stringify(p.additional_fields) : null,
            parentId,
            topParentId,
            p.level ?? 1,
            p.row_id,
            p.status ?? "out_of_stock",
          ]
        );

        const productId = productInsert.rows[0].id;
        rowIdToId.set(p.row_id, productId);

        // Insert others_thresholds
        if (p.others_thresholds?.length) {
          // eslint-disable-next-line no-restricted-syntax
          for (const t of p.others_thresholds) {
            await client.query(
              `
              INSERT INTO others_thresholds (id, product_id, value, color, tag)
              VALUES (gen_random_uuid(), $1, $2, $3, $4)
              `,
              [productId, t.value, t.color ?? null, t.type ?? null]
            );
          }
        }

        // Insert vendors

        if (p.vendors?.length) {
          // eslint-disable-next-line no-restricted-syntax
          for (const v of p.vendors) {
            await client.query(
              `
              INSERT INTO product_vendors (product_id, vendor_id, vendor_name)
              VALUES ($1, $2, $3)
              ON CONFLICT (product_id, vendor_id) DO NOTHING
              `,
              [productId, v.vendor_id, v.vendor_name ?? null]
            );
          }
        }

        // Insert attachments
        if (p.attachments?.length) {
          // eslint-disable-next-line no-restricted-syntax
          for (const a of p.attachments) {
            await client.query(
              `
              INSERT INTO product_attachments (product_id, media_id)
              VALUES ($1, $2)
              ON CONFLICT DO NOTHING
              `,
              [productId, a.id]
            );
          }
        }

        // Insert forms
        if (p.form) {
          await client.query(
            `
            INSERT INTO product_forms (id, product_id, form_id, ref_form_data)
            VALUES (gen_random_uuid(), $1, $2, $3)
            `,
            [
              [productId],
              p.form.form_id,
              JSON.stringify(p.form.ref_form_data ?? {}),
            ]
          );
        }

        // Insert stores and propagate to ancestors
        if (p.stores?.length) {
          // eslint-disable-next-line no-restricted-syntax
          for (const s of p.stores) {
            await client.query(
              `
              INSERT INTO product_stores (id, product_id, store_id, type, quantity, scrap, semi_finished_quantity, rejected_quantity)
          }

              VALUES (gen_random_uuid(), $1, $2, $3, $4, $5, $6, $7)
              ON CONFLICT (product_id, store_id, type) DO UPDATE
                SET quantity = EXCLUDED.quantity,
                    scrap = EXCLUDED.scrap,
                    semi_finished_quantity = EXCLUDED.semi_finished_quantity,
                    rejected_quantity = EXCLUDED.rejected_quantity
              `,
              [
                productId,
                s.store_id,
                s.type ?? "normal",
                s.quantity ?? 0,
                s.scrap ?? 0,
                s.semi_finished_quantity ?? 0,
                s.rejected_quantity ?? 0,
              ]
            );
          }
        }
      }

      // Return all inserted products
      const allIds = Array.from(rowIdToId.values());
      const result = await client.query(
        `SELECT * FROM products WHERE id = ANY($1)`,
        [allIds]
      );
      return result.rows;
    });
  }

  /**
   * Insert a product_store row and propagate store access to all parents
   */
  // private async insertStoreWithPropagation(
  //   client: PoolClient,
  //   productId: string,
  //   store: any,
  //   rowIdToId: Map<string, string>,
  //   payload: CreateProductType
  // ) {
  //   // Insert current product store
  //   await client.query(
  //     `
  //     INSERT INTO product_stores (id, product_id, store_id, type, quantity, scrap, semi_finished_quantity, rejected_quantity)
  //     VALUES (gen_random_uuid(), $1, $2, $3, $4, $5, $6, $7)
  //     ON CONFLICT (product_id, store_id, type) DO UPDATE
  //       SET quantity = EXCLUDED.quantity,
  //           scrap = EXCLUDED.scrap,
  //           semi_finished_quantity = EXCLUDED.semi_finished_quantity,
  //           rejected_quantity = EXCLUDED.rejected_quantity
  //     `,
  //     [
  //       productId,
  //       store.store_id,
  //       store.type,
  //       store.quantity ?? 0,
  //       store.scrap ?? 0,
  //       store.semi_finished_quantity ?? 0,
  //       store.rejected_quantity ?? 0,
  //     ]
  //   );

  //   // Propagate to ancestors
  //   let current = payload.find((p) => rowIdToId.get(p.row_id) === productId);
  //   while (current?.level > 1) {
  //     const parentRowId = current.row_id.split(".").slice(0, -1).join(".");
  //     const parentId = rowIdToId.get(parentRowId)!;

  //     await client.query(
  //       `
  //       INSERT INTO product_stores (id, product_id, store_id, type)
  //       VALUES (gen_random_uuid(), $1, $2, $3)
  //       ON CONFLICT (product_id, store_id, type) DO NOTHING
  //       `,
  //       [parentId, store.store_id, store.type]
  //     );

  //     current = payload.find((p) => p.row_id === parentRowId);
  //   }
  // }
}
