export interface VendorAddressDto {
  id?: string;
  line1: string;
  line2?: string;
  city: string;
  state: string;
  country: string;
  pin: string;
}

export interface VendorTagDto {
  id?: string;
  tagId: string;
  tagName?: string;
  tagColor?: string;
}

export interface CreateVendorDto {
  name: string;
  email: string;
  phone?: string;
  type: "vendor" | "customer" | "both";

  companyName?: string;
  gstNumber?: string;
  companyPhones?: string[];
  companyEmails?: string[];

  addresses?: VendorAddressDto[];
  tags?: VendorTagDto[];
  paymentTermId: string;
}

export interface UpdateVendorDto extends Partial<CreateVendorDto> {}

export interface VendorDto {
  id: string;
  name: string;
  email: string;
  phone?: string;
  type: "vendor" | "customer" | "both";

  companyName?: string;
  gstNumber?: string;
  companyPhones?: string[];
  companyEmails?: string[];

  addresses?: VendorAddressDto[];
  tags?: VendorTagDto[];
  paymentTermId: string;

  created_at: string;
  updated_at: string;
}
