
CREATE TABLE media (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    data TEXT, -- could also use BYTEA if storing binary
    name VA<PERSON><PERSON><PERSON>(255),
    type VA<PERSON>HA<PERSON>(100),
    s3_key VARCHAR(255),
    refs TEXT[] DEFAULT '{}', -- array of string references
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
