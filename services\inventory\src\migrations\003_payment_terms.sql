-- Payment Terms
CREATE TABLE IF NOT EXISTS payment_terms (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    term_type VARCHAR(50) NOT NULL UNIQUE, 
    term_description TEXT,
    days INTEGER CHECK (days > 0), 
    percentage DECIMAL(5,2) CHECK (percentage > 0 AND percentage <= 100), 
    is_predefined BOOLEAN DEFAULT FALSE, 
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);