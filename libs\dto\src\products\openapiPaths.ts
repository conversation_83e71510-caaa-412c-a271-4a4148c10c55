import { registry } from "../utils/openapiRegistry";
import {
  CreateProductSchema,
  DeleteProductSchema,
  GetProductSchema,
  ListProductsQuerySchema,
  ProductSchema,
  UpdateProductSchema,
} from "./index";

export const registerProductPaths = () => {
  registry.registerPath({
    method: "post",
    path: "/products",
    description: "Create a new product",
    tags: ["Products"],
    security: [{ bearerAuth: [] }],
    request: {
      body: {
        content: { "application/json": { schema: CreateProductSchema } },
      },
    },
    responses: {
      201: {
        description: "Product created",
        content: { "application/json": { schema: CreateProductSchema } },
      },
    },
  });

  registry.registerPath({
    method: "put",
    path: "/products/{id}",
    description: "Update a product",
    tags: ["Products"],
    security: [{ bearerAuth: [] }],
    request: {
      body: {
        content: { "application/json": { schema: UpdateProductSchema } },
      },
      params: ProductSchema.pick({ id: true }),
    },
    responses: {
      200: {
        description: "Product updated",
        content: { "application/json": { schema: UpdateProductSchema } },
      },
    },
  });

  registry.registerPath({
    method: "get",
    path: "/products/{id}",
    description: "Get a product",
    tags: ["Products"],
    security: [{ bearerAuth: [] }],
    request: {
      params: GetProductSchema,
    },
    responses: {
      200: {
        description: "Product details",
        content: { "application/json": { schema: CreateProductSchema } },
      },
    },
  });

  registry.registerPath({
    method: "get",
    path: "/products",
    description: "Get all products",
    tags: ["Products"],
    security: [{ bearerAuth: [] }],
    request: {
      query: ListProductsQuerySchema,
    },
    responses: {
      200: {
        description: "List of products",
        content: {
          "application/json": { schema: CreateProductSchema.array() },
        },
      },
    },
  });

  registry.registerPath({
    method: "delete",
    path: "/products/{id}",
    description: "Delete a product",
    tags: ["Products"],
    security: [{ bearerAuth: [] }],
    request: {
      params: DeleteProductSchema,
    },
    responses: {
      204: {
        description: "Product deleted",
      },
    },
  });
};
