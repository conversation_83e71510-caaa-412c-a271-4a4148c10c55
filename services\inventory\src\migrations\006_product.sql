
CREATE TABLE IF NOT EXISTS products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(), -- id of the row to uniquely identify the row and for relations
    name VARCHAR(255) NOT NULL,
    item_id VARCHAR(255), -- prefix id
    product_category VARCHAR(20) CHECK (product_category IN ('product', 'service')),
    category VARCHAR(255),
    item_type VARCHAR(20) CHECK (item_type IN ('buy', 'sell', 'both')),
    uom VARCHAR(50),
    default_buy_price NUMERIC(12,2),
    default_sell_price NUMERIC(12,2),
    tax_percentage NUMERIC(5,2),
    hsn_code VARCHAR(50),
    description TEXT,
    thumbnail UUID REFERENCES media(id),
    is_used BOOLEAN DEFAULT FALSE,
    requires_inspections BOOLEAN DEFAULT FALSE,
    quantity_threshold INT DEFAULT 0,
    quantity INT DEFAULT 0,
    semifinished_quantity INT DEFAULT 0,
    scrap INT DEFAULT 0,
    rejected_quantity INT DEFAULT 0,
    additional_fields JSONB,
    parent_id UUID REFERENCES products(id),
    top_parent_id UUID REFERENCES products(id),
    level INT DEFAULT 1,
    row_id VARCHAR(255),
    status VARCHAR(50) DEFAULT 'out_of_stock',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);


CREATE TABLE product_lineage (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    parent_names TEXT[], -- array of all parent names just above the product
    parent_ids UUID[] -- array of all parent ids just above the product
);

CREATE TABLE IF NOT EXISTS product_forms (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    product_id UUID[] REFERENCES products(id) ON DELETE CASCADE,
    form_id UUID REFERENCES forms(id),
    ref_form_data JSONB
);


CREATE TABLE IF NOT EXISTS others_thresholds (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    value INT NOT NULL,
    color VARCHAR(50),
    tag VARCHAR(100)
);


CREATE TABLE IF NOT EXISTS product_attachments (
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    media_id UUID REFERENCES media(id),
    PRIMARY KEY (product_id, media_id)
);

-- if a product has lets stores access if S-001 and S-002  and its child has stores S-001, S-002 and S-003 then all the parent above it must hava access of S-001, S-002 and S-003  

-- CREATE TABLE product_stores (
--     id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
--     product_id UUID[] REFERENCES products(id) ON DELETE CASCADE, -- array of all product for this store 
--     store_id UUID REFERENCES stores(id) ON DELETE CASCADE,
--     type VARCHAR(20) CHECK (type IN ('normal','rejected',)) NOT NULL,
--     quantity INT DEFAULT 0,
--     scrap INT DEFAULT 0,
--     semi_finished_quantity INT DEFAULT 0,
--     rejected_quantity INT DEFAULT 0
-- );

CREATE TABLE product_stores (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    store_id UUID REFERENCES stores(id) ON DELETE CASCADE,
    type VARCHAR(20) CHECK (type IN ('normal','rejected')) NOT NULL,
    quantity INT DEFAULT 0,
    scrap INT DEFAULT 0,
    semi_finished_quantity INT DEFAULT 0,
    rejected_quantity INT DEFAULT 0,
    UNIQUE (product_id, store_id, type)
);


CREATE TABLE product_vendors (
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    vendor_id UUID REFERENCES vendors(id),
    vendor_name VARCHAR(255),
    product_name VARCHAR(255),
    price NUMERIC(12,2),
    lead_time INT, -- in days
    PRIMARY KEY (product_id, vendor_id)
);


